version: '3.8'

services:
  # 🎤 NVIDIA STT Service - Polish FastConformer
  nvidia-stt-polish:
    image: nvcr.io/nvidia/nemo:24.09
    container_name: hvac-nvidia-stt-polish
    restart: unless-stopped
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - NEMO_MODEL_NAME=stt_multilingual_fastconformer_hybrid_large_pc
      - LANGUAGE=pl
      - SAMPLE_RATE=16000
      - BATCH_SIZE=4
      - MAX_DURATION=300
    volumes:
      - ./models:/workspace/models
      - ./audio_input:/workspace/audio_input
      - ./transcriptions:/workspace/transcriptions
      - ./scripts:/workspace/scripts
      - ./logs:/workspace/logs
    ports:
      - "8888:8888"  # Jupyter/API
      - "8889:8889"  # STT Service
    # deploy: GPU wyłączone - używamy CPU
    # resources:
    #   reservations:
    #     devices:
    #       - driver: nvidia
    #         count: 1
    #         capabilities: [gpu]
    command: >
      bash -c "
        echo '🚀 Inicjalizacja NVIDIA STT dla języka polskiego...' &&
        echo '⚠️ Uruchamianie bez GPU - tryb testowy' &&
        python /workspace/scripts/stt_server.py
      "
    networks:
      - hvac-network

  # 🔄 Audio Converter Service (M4A -> WAV)
  audio-converter:
    build:
      context: ./audio-converter
      dockerfile: Dockerfile
    container_name: hvac-audio-converter
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis-krabulon:6379
      - INPUT_FORMAT=m4a
      - OUTPUT_FORMAT=wav
      - SAMPLE_RATE=16000
      - CHANNELS=1
    volumes:
      - ./audio_input:/app/input
      - ./audio_converted:/app/output
      - ./logs:/app/logs
    # depends_on: używamy zewnętrznego Redis
    networks:
      - hvac-network

  # 📧 Email Processor Service
  email-processor:
    build:
      context: ./email-processor
      dockerfile: Dockerfile
    container_name: hvac-email-processor
    restart: unless-stopped
    environment:
      - IMAP_SERVER=imap.gmail.com
      - IMAP_PORT=993
      - EMAIL_DOLORES=<EMAIL>
      - EMAIL_GRZEGORZ=<EMAIL>
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=********************************************************/hvac_crm
      - MINIO_ENDPOINT=**************:9000
      - MINIO_ACCESS_KEY=koldbringer
      - MINIO_SECRET_KEY=Blaeritipol1
    volumes:
      - ./email_attachments:/app/attachments
      - ./logs:/app/logs
    depends_on:
      - audio-converter
    networks:
      - hvac-network

  # 🧠 Gemma 3 4B Integration Service
  gemma-integration:
    build:
      context: ./gemma-integration
      dockerfile: Dockerfile
    container_name: hvac-gemma-integration
    restart: unless-stopped
    environment:
      - LM_STUDIO_URL=http://*************:1234
      - MODEL_NAME=gemma-3-4b
      - LANGUAGE=pl
      - MAX_TOKENS=2048
      - TEMPERATURE=0.3
      - REDIS_URL=redis://redis-krabulon:6379
    volumes:
      - ./transcriptions:/app/transcriptions
      - ./analysis_results:/app/analysis
      - ./logs:/app/logs
    # depends_on: używamy zewnętrznego Redis
    networks:
      - hvac-network

  # 📊 Redis Cache - używamy zewnętrznego Redis na localhost:6379

  # 🎯 Transcription Orchestrator
  transcription-orchestrator:
    build:
      context: ./orchestrator
      dockerfile: Dockerfile
    container_name: hvac-transcription-orchestrator
    restart: unless-stopped
    environment:
      - NVIDIA_STT_URL=http://nvidia-stt-polish:8889
      - AUDIO_CONVERTER_URL=http://audio-converter:8080
      - EMAIL_PROCESSOR_URL=http://email-processor:8080
      - GEMMA_INTEGRATION_URL=http://gemma-integration:8080
      - GOBACKEND_URL=http://host.docker.internal:8080
      - REDIS_URL=redis://redis-krabulon:6379
      - POSTGRES_URL=********************************************************/hvac_crm
    ports:
      - "9000:8080"  # Main API
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - audio-converter
      - email-processor
      - gemma-integration
    networks:
      - hvac-network

# volumes: używamy zewnętrznego Redis

networks:
  hvac-network:
    external: true
    name: python_mixer_hvac-network
